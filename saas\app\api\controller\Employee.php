<?php

namespace app\api\controller;

use app\BaseController;
use app\api\model\Union as UnionModel;
use app\api\model\Employee as EmployeeModel;
use app\api\service\JwtService;
use think\facade\Cache;
use think\facade\Request;


class Employee extends BaseController
{
    /**
     * 员工列表（首页列表）
     * @return \think\response\Json
     */
    public function index()
    {
        echo $this->tenant_id;die;
        try {
            // 获取筛选参数
            $filterName = $this->request->param('filter_name', '');
            $filterDepartment = $this->request->param('filter_department', '');
            $filterStatus = $this->request->param('filter_status', '');
            $filterDateStart = $this->request->param('filter_date_start', '');
            $filterDateEnd = $this->request->param('filter_date_end', '');

            // 获取排序参数
            $sort = $this->request->param('sort', 'job_work_date');
            $order = $this->request->param('order', 'desc');

            // 获取分页参数
            $page = (int)$this->request->param('page', 1);
            $limit = (int)$this->request->param('limit', 50);

            // 构建筛选条件
            $filter = [
                'filter_name' => $filterName,
                'filter_department' => $filterDepartment,
                'filter_status' => $filterStatus,
                'filter_date_start' => $filterDateStart,
                'filter_date_end' => $filterDateEnd,
                'sort' => $sort,
                'order' => $order,
                'page' => $page,
                'limit' => $limit,
            ];

            // 实例化员工模型
            $employeeModel = new EmployeeModel();

            // 获取员工列表数据
            $result = $employeeModel->getEmployeeList($filter,$union_info['tenant_id']);

            // 处理员工数据
            $employees = [];
            foreach ($result->items() as $employee) {
                $employees[] = [
                    'id' => $employee['employee_id'],
                    'name' => $employee['fullname'],
                    'phone' => $employee['telephone'],
                    'age' => $employeeModel->getAge($employee['birth_date']),
                    'job' => $employee['job_department'] . $employee['job_post'],
                    'jobage' => $employeeModel->getJobAge($employee['job_work_date'], $employee['job_leave_date']),
                    'entry' => $employee['job_work_date'],
                    'status' => $employee['job_status'],
                    'from' => $employee['channel_from'],
                ];
            }

            // 获取辅助数据
            $statuses = $employeeModel->getStatuses();
            $departments = $employeeModel->getDepartments();
            $channels = $employeeModel->getChannels();

            // 构建返回数据
            $data = [
                'list' => $employees,
                'pagination' => [
                    'total' => $result->total(),
                    'per_page' => $result->listRows(),
                    'current_page' => $result->currentPage(),
                    'last_page' => $result->lastPage(),
                ],
                'filters' => [
                    'filter_name' => $filterName,
                    'filter_department' => $filterDepartment,
                    'filter_status' => $filterStatus,
                    'filter_date_start' => $filterDateStart,
                    'filter_date_end' => $filterDateEnd,
                ],
                'sort' => [
                    'field' => $sort,
                    'order' => $order,
                ],
                'options' => [
                    'statuses' => $statuses,
                    'departments' => $departments,
                    'channels' => $channels,
                ],
            ];

            return $this->success($data, '获取员工列表成功');

        } catch (\Exception $e) {
            return $this->error('获取员工列表失败：' . $e->getMessage());
        }
    }

    /**
     * 添加员工
     * @return \think\response\Json
     */
    public function add()
    {
        $tenantId = $this->request->tenant_id;

        try {
            // 验证请求方法
            if (!$this->request->isPost()) {
                return $this->error('请求方法错误');
            }

            // 获取POST数据
            $data = $this->request->post();

            // 数据验证
            $validateResult = $this->validateEmployeeData($data);
            if ($validateResult !== true) {
                return $this->error($validateResult);
            }

            // 获取租户ID（这里需要根据实际情况获取）

            // 实例化员工模型
            $employeeModel = new EmployeeModel();

            // 添加员工
            $employeeId = $employeeModel->addEmployee($data, $tenantId);

            if ($employeeId) {
                return $this->success(['employee_id' => $employeeId], '员工添加成功');
            } else {
                return $this->error('员工添加失败');
            }

        } catch (\Exception $e) {
            return $this->error('员工添加失败：' . $e->getMessage());
        }
    }

    /**
     * 编辑员工
     * @return \think\response\Json
     */
    public function edit()
    {
        try {
            // 验证请求方法
            if (!$this->request->isPost()) {
                return $this->error('请求方法错误');
            }

            // 获取员工ID
            $employeeId = (int)$this->request->param('employee_id', 0);
            if (!$employeeId) {
                return $this->error('员工ID不能为空');
            }

            // 获取POST数据
            $data = $this->request->post();

            // 数据验证
            $validateResult = $this->validateEmployeeData($data);
            if ($validateResult !== true) {
                return $this->error($validateResult);
            }

            // 实例化员工模型
            $employeeModel = new EmployeeModel();

            // 检查员工是否存在
            $employee = $employeeModel->getEmployeeDetail($employeeId);
            if (!$employee) {
                return $this->error('员工不存在');
            }

            // 编辑员工
            $result = $employeeModel->editEmployee($employeeId, $data);

            if ($result) {
                return $this->success(['employee_id' => $employeeId], '员工编辑成功');
            } else {
                return $this->error('员工编辑失败');
            }

        } catch (\Exception $e) {
            return $this->error('员工编辑失败：' . $e->getMessage());
        }
    }

    /**
     * 验证员工数据
     * @param array $data 员工数据
     * @return string|true 验证结果，true表示通过，字符串表示错误信息
     */
    private function validateEmployeeData($data)
    {
        // 姓名验证
        if (empty($data['fullname'])) {
            return '姓名不能为空！';
        }

        // 手机号验证
        if (empty($data['telephone'])) {
            return '手机号不能为空！';
        }

        // 手机号格式验证
        if (!preg_match('/^1[3-9]\d{9}$/', $data['telephone'])) {
            return '手机号格式不正确！';
        }

        // 部门验证
        if (empty($data['department'])) {
            return '所属部门不能为空！';
        }

        // 身份证验证
        if (empty($data['card_no']) || empty($data['card_name'])) {
            return '身份证信息不能为空！';
        }

        // 身份证号格式验证
        if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $data['card_no'])) {
            return '身份证号格式不正确！';
        }

        // 紧急联系人验证
        if (empty($data['contact_name']) || empty($data['contact_telephone'])) {
            return '紧急联系人信息不能为空！';
        }

        // 出生日期验证
        if (empty($data['birth_date'])) {
            return '出生日期不能为空！';
        }

        // 入职时间验证
        if (empty($data['job_work_date'])) {
            return '入职时间不能为空！';
        }

        // 预计转正时间验证
        if (empty($data['job_estimate_date'])) {
            return '预计转正时间不能为空！';
        }

        // 日期格式验证
        $dateFields = ['birth_date', 'job_work_date', 'job_estimate_date'];
        foreach ($dateFields as $field) {
            if (!empty($data[$field]) && !strtotime($data[$field])) {
                return $field . '日期格式不正确！';
            }
        }

        return true;
    }
}
